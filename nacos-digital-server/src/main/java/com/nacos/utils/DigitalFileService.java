package com.nacos.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.business.utils.BOssUtil;
import com.nacos.config.DigitalOssConfigManager;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.UUID;

/**
 * Digital模块文件服务
 * 支持可配置的OSS上传功能
 */
@Slf4j
@Service
public class DigitalFileService {
    
    @Autowired
    private DigitalOssConfigManager configManager;

    @Autowired
    private DigitalOssClientManager clientManager;

    // 支持手动设置依赖（用于ConfigurableBOssUtil）
    public void setConfigManager(DigitalOssConfigManager configManager) {
        this.configManager = configManager;
    }

    public void setClientManager(DigitalOssClientManager clientManager) {
        this.clientManager = clientManager;
    }
    
    /**
     * 统一的数字人资源上传方法（Spring管理版本）
     *
     * @param source   源文件（支持byte[]、MultipartFile、String URL）
     * @param fileName 文件名（可选，为null时自动生成）
     * @param userId   用户ID
     * @param groupId  分组ID
     * @param type     文件类型
     * @param isSystem 是否系统文件
     * @return 上传后的OSS路径
     */
    public String uploadDigitalResource(Object source, String fileName, String userId, String groupId, Integer type, Boolean isSystem) {
        if (source == null) {
            log.error("上传源文件不能为空");
            return null;
        }
        
        // 如果未启用自定义配置，使用原有的BOssUtil
        if (!configManager.isCustomConfigEnabled()) {
            log.debug("使用原有BOssUtil进行文件上传");
            return DigitalFileUtil.uploadDigitalResource(source, fileName, userId, groupId, type, isSystem);
        }
        
        // 使用新的配置进行上传
        log.debug("使用Digital自定义OSS配置进行文件上传");
        
        // 生成文件名
        if (StringUtils.isBlank(fileName)) {
            fileName = UUID.randomUUID().toString();
        }
        
        // 根据source类型选择上传方式
        if (source instanceof byte[]) {
            return uploadWithNewConfig((byte[]) source, fileName, userId, groupId, type);
        } else if (source instanceof MultipartFile) {
            try {
                return uploadWithNewConfig(((MultipartFile) source).getBytes(), fileName, userId, groupId, type);
            } catch (IOException e) {
                log.error("读取MultipartFile内容失败", e);
                return null;
            }
        } else if (source instanceof String) {
            String url = (String) source;
            if (StringUtils.isBlank(url)) {
                log.error("URL不能为空");
                return null;
            }
            return uploadUrlWithNewConfig(url, fileName, userId, groupId, type);
        } else {
            log.error("不支持的源文件类型: {}", source.getClass().getName());
            return null;
        }
    }
    
    /**
     * 使用新配置上传字节数组
     */
    private String uploadWithNewConfig(byte[] fileBytes, String fileName, String userId, String groupId, Integer type) {
        if (fileBytes == null || fileBytes.length == 0) {
            return null;
        }
        
        try {
            return handleDigitalFileUpload(
                new ByteArrayInputStream(fileBytes),
                fileBytes.length,
                fileName,
                userId,
                groupId,
                type
            );
        } catch (Exception e) {
            log.error("使用新配置上传文件失败", e);
            return null;
        }
    }
    
    /**
     * 使用新配置上传URL文件
     */
    private String uploadUrlWithNewConfig(String fileUrl, String fileName, String userId, String groupId, Integer type) {
        try {
            URI uri = new URI(fileUrl);
            URL url = uri.toURL();
            return handleDigitalFileUpload(
                url.openStream(),
                -1,
                fileName,
                userId,
                groupId,
                type
            );
        } catch (Exception e) {
            log.error("使用新配置上传URL文件失败", e);
            return null;
        }
    }
    
    /**
     * 处理数字人文件上传的核心逻辑
     */
    private String handleDigitalFileUpload(InputStream inputStream, long contentLength, 
            String fileName, String userId, String groupId, Integer type) {
        
        // 获取文件后缀
        String suffix = switch (type) {
            case 0, 1, 2, 3, 11, 13 -> OssClientConfig.FILE_SUFFIX_VIDEO;  // .mp4
            case 4, 5, 10, 12 -> OssClientConfig.FILE_SUFFIX_AUDIO;        // .mp3
            case 6, 7, 8, 14 -> OssClientConfig.FILE_SUFFIX;               // .webp
            case 9 -> ".txt"; // 知识库文件
            default -> OssClientConfig.FILE_SUFFIX;
        };
        
        // 构建完整的存储路径
        String objectName = OssClientConfig.getDigitalPath(userId, groupId, type).concat(fileName).concat(suffix);
        
        // 获取OSS客户端
        OSS ossClient = clientManager.getOssClient();
        
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                configManager.getBucketName(),
                objectName,
                inputStream
            );
            
            // 设置文件元信息
            ObjectMetadata metadata = new ObjectMetadata();
            if (contentLength >= 0) {
                metadata.setContentLength(contentLength);
            }
            switch (type) {
                case 0, 1, 2, 3, 11, 13 -> metadata.setContentType("video/mp4");
                case 4, 5, 10, 12 -> metadata.setContentType("audio/mpeg");
                case 6, 7, 8, 14 -> metadata.setContentType("image/webp");
                case 9 -> metadata.setContentType("text/plain");
            }
            putObjectRequest.setMetadata(metadata);
            
            // 执行上传
            ossClient.putObject(putObjectRequest);
            
            // 构建访问URL
            String accessUrl = configManager.getAccessPath() + objectName;
            log.info("文件上传成功，访问URL: {}", accessUrl);
            return accessUrl;
            
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }
    
    /**
     * 检查配置状态
     */
    public String getConfigStatus() {
        return configManager.getConfigSummary();
    }
}
