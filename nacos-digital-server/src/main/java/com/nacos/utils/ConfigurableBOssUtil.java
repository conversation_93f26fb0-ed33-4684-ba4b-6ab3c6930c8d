package com.nacos.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.AsyncProcessObjectResult;
import com.business.utils.BOssUtil;
import com.nacos.config.DigitalOssConfigManager;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.InputStream;

/**
 * 可配置的BOssUtil包装器
 * 完全兼容原有BOssUtil的所有方法，但支持配置化OSS
 */
@Slf4j
@Component
public class ConfigurableBOssUtil {
    
    @Autowired(required = false)
    private DigitalOssConfigManager configManager;
    
    @Autowired(required = false)
    private DigitalOssClientManager clientManager;
    
    // 静态实例，供静态方法使用
    private static ConfigurableBOssUtil instance;
    
    @PostConstruct
    public void init() {
        instance = this;
        log.info("ConfigurableBOssUtil初始化完成，配置管理器: {}", 
            configManager != null ? "已启用" : "未启用");
    }
    
    /**
     * 上传数字人文件（完全兼容BOssUtil.uploadDigitalFile方法）
     */
    public static String uploadDigitalFile(Object source, String fileName, String userId, String groupId, Integer type) {
        // 如果没有配置管理器或未启用自定义配置，使用原有BOssUtil
        if (instance == null || instance.configManager == null || !instance.configManager.isCustomConfigEnabled()) {
            log.debug("使用原有BOssUtil.uploadDigitalFile");
            return BOssUtil.uploadDigitalFile(source, fileName, userId, groupId, type);
        }
        
        // 使用新的配置化上传
        log.debug("使用配置化OSS上传数字人文件");
        return instance.uploadWithCustomConfig(source, fileName, userId, groupId, type);
    }
    
    /**
     * 上传数字人文件（带系统标识，完全兼容BOssUtil方法）
     */
    public static String uploadDigitalFile(Object source, String fileName, String userId, String groupId, Integer type, Boolean isSystem) {
        // 如果没有配置管理器或未启用自定义配置，使用原有BOssUtil
        if (instance == null || instance.configManager == null || !instance.configManager.isCustomConfigEnabled()) {
            log.debug("使用原有BOssUtil.uploadDigitalFile（带系统标识）");
            return BOssUtil.uploadDigitalFile(source, fileName, userId, groupId, type, isSystem);
        }
        
        // 使用新的配置化上传
        log.debug("使用配置化OSS上传数字人文件（带系统标识）");
        return instance.uploadWithCustomConfig(source, fileName, userId, groupId, type);
    }
    
    /**
     * 获取数字人OSS实例（兼容BOssUtil.getDigitalOssInstance）
     */
    public static OSS getDigitalOssInstance() {
        // 如果没有配置管理器或未启用自定义配置，使用原有BOssUtil
        if (instance == null || instance.configManager == null || !instance.configManager.isCustomConfigEnabled()) {
            return BOssUtil.getDigitalOssInstance();
        }
        
        // 使用新的配置化客户端
        return instance.clientManager.getOssClient();
    }
    
    /**
     * 删除文件（兼容BOssUtil方法）
     */
    public static boolean deleteFile(String objectName) {
        try {
            OSS ossClient = getDigitalOssInstance();
            String bucketName = getBucketName();
            ossClient.deleteObject(bucketName, objectName);
            log.info("删除文件成功: {}", objectName);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败: {}", objectName, e);
            return false;
        }
    }
    
    /**
     * 检查文件是否存在（兼容BOssUtil方法）
     */
    public static boolean doesObjectExist(String objectName) {
        try {
            OSS ossClient = getDigitalOssInstance();
            String bucketName = getBucketName();
            return ossClient.doesObjectExist(bucketName, objectName);
        } catch (Exception e) {
            log.error("检查文件是否存在失败: {}", objectName, e);
            return false;
        }
    }
    
    /**
     * 获取当前使用的bucket名称
     */
    private static String getBucketName() {
        if (instance != null && instance.configManager != null && instance.configManager.isCustomConfigEnabled()) {
            return instance.configManager.getBucketName();
        }
        return OssClientConfig.BUCKET_NAME;
    }
    
    /**
     * 获取当前使用的访问路径
     */
    private static String getAccessPath() {
        if (instance != null && instance.configManager != null && instance.configManager.isCustomConfigEnabled()) {
            return instance.configManager.getAccessPath();
        }
        return OssClientConfig.ACCESS_PATH;
    }
    
    /**
     * 使用自定义配置上传文件的内部实现
     */
    private String uploadWithCustomConfig(Object source, String fileName, String userId, String groupId, Integer type) {
        try {
            // 委托给DigitalFileService处理
            DigitalFileService fileService = new DigitalFileService();
            fileService.setConfigManager(configManager);
            fileService.setClientManager(clientManager);
            
            return fileService.uploadDigitalResource(source, fileName, userId, groupId, type, null);
        } catch (Exception e) {
            log.error("使用自定义配置上传文件失败", e);
            return null;
        }
    }
    
    /**
     * 获取配置状态
     */
    public static String getConfigStatus() {
        if (instance != null && instance.configManager != null) {
            return instance.configManager.getConfigSummary();
        }
        return "使用原有硬编码配置";
    }
    
    /**
     * 强制重新创建OSS客户端（配置变更时使用）
     */
    public static void recreateClient() {
        if (instance != null && instance.clientManager != null) {
            instance.clientManager.recreateClient();
        } else {
            log.warn("无法重新创建客户端：配置管理器未初始化");
        }
    }
    
    // 为了支持DigitalFileService的依赖注入
    public void setConfigManager(DigitalOssConfigManager configManager) {
        this.configManager = configManager;
    }
    
    public void setClientManager(DigitalOssClientManager clientManager) {
        this.clientManager = clientManager;
    }
}
