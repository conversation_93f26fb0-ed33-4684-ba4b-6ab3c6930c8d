package com.nacos.utils;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.nacos.config.DigitalOssConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * Digital模块专用的OSS客户端管理器
 * 支持动态配置和连接管理
 */
@Slf4j
@Component
public class DigitalOssClientManager {
    
    @Autowired
    private DigitalOssConfigManager configManager;
    
    private volatile OSS ossClient;
    private final Object lock = new Object();
    
    @PostConstruct
    public void init() {
        log.info("初始化Digital OSS客户端管理器");
        log.info("当前配置: {}", configManager.getConfigSummary());
    }
    
    /**
     * 获取OSS客户端实例
     */
    public OSS getOssClient() {
        if (ossClient == null) {
            synchronized (lock) {
                if (ossClient == null) {
                    ossClient = createOssClient();
                    log.info("创建新的Digital OSS客户端实例");
                    
                    // 添加JVM关闭钩子
                    Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
                }
            }
        }
        return ossClient;
    }
    
    /**
     * 创建OSS客户端
     */
    private OSS createOssClient() {
        ClientBuilderConfiguration config = createClientConfig();
        
        return new OSSClientBuilder().build(
            configManager.getEndpoint(),
            configManager.getAccessKeyId(),
            configManager.getSecretAccessKey(),
            config
        );
    }
    
    /**
     * 创建客户端配置
     */
    private ClientBuilderConfiguration createClientConfig() {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        
        // 使用配置管理器的值
        conf.setConnectionTimeout(configManager.getConnectionTimeout());
        conf.setSocketTimeout(configManager.getSocketTimeout());
        conf.setMaxErrorRetry(configManager.getMaxErrorRetry());
        
        // 其他固定配置
        conf.setMaxConnections(200);
        conf.setIdleConnectionTime(300);
        conf.setConnectionTTL(600_000);
        conf.setSupportCname(true);
        
        return conf;
    }
    
    /**
     * 检查并重新初始化连接
     */
    public void checkAndReinitialize() {
        synchronized (lock) {
            try {
                if (ossClient != null && !ossClient.doesBucketExist(configManager.getBucketName())) {
                    log.warn("OSS连接异常，尝试重建连接...");
                    shutdown();
                    ossClient = createOssClient();
                    log.info("OSS连接已重建");
                }
            } catch (Exception e) {
                log.error("检查OSS连接状态时发生异常", e);
                shutdown();
                ossClient = createOssClient();
            }
        }
    }
    
    /**
     * 关闭OSS客户端
     */
    @PreDestroy
    public void shutdown() {
        synchronized (lock) {
            if (ossClient != null) {
                try {
                    ossClient.shutdown();
                    ossClient = null;
                    log.info("Digital OSS客户端已正确释放");
                } catch (Exception e) {
                    log.error("关闭Digital OSS客户端时发生异常", e);
                }
            }
        }
    }
    
    /**
     * 强制重新创建客户端（配置变更时使用）
     */
    public void recreateClient() {
        synchronized (lock) {
            log.info("强制重新创建OSS客户端");
            shutdown();
            ossClient = createOssClient();
            log.info("OSS客户端重新创建完成，新配置: {}", configManager.getConfigSummary());
        }
    }
}
