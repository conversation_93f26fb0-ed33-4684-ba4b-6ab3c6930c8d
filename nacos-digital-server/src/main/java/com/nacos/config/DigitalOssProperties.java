package com.nacos.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Digital模块专用OSS配置属性
 * 支持通过配置文件动态配置OSS参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "digital.oss")
public class DigitalOssProperties {
    
    /**
     * 是否启用自定义OSS配置
     * false: 使用原有硬编码配置（默认）
     * true: 使用配置文件中的自定义配置
     */
    private boolean enabled = false;
    
    /**
     * OSS服务端点
     */
    private String endpoint = "https://oss-cn-beijing.aliyuncs.com";
    
    /**
     * OSS访问密钥ID
     */
    private String accessKeyId = "LTAI5t9A9sRyNs1a2AsDT7Hp";
    
    /**
     * OSS访问密钥Secret
     */
    private String secretAccessKey = "******************************";
    
    /**
     * OSS存储桶名称
     */
    private String bucketName = "idotdesign";
    
    /**
     * OSS访问路径前缀
     */
    private String accessPath = "https://cdn.diandiansheji.com/";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 10000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int socketTimeout = 15000;
    
    /**
     * 最大重试次数
     */
    private int maxErrorRetry = 3;
}
