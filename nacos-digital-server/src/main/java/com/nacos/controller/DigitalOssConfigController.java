package com.nacos.controller;

import com.nacos.config.DigitalOssConfigManager;
import com.nacos.config.DigitalOssProperties;
import com.nacos.result.Result;
import com.nacos.utils.DigitalFileService;
import com.nacos.utils.DigitalOssClientManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Digital OSS配置管理接口
 */
@Slf4j
@RestController
@RequestMapping("/admin/oss-config")
@Tag(name = "Digital OSS配置管理", description = "管理Digital模块的OSS配置")
public class DigitalOssConfigController {
    
    @Autowired
    private DigitalOssConfigManager configManager;
    
    @Autowired
    private DigitalOssProperties ossProperties;
    
    @Autowired
    private DigitalOssClientManager clientManager;
    
    @Autowired
    private DigitalFileService fileService;
    
    /**
     * 获取当前OSS配置状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取OSS配置状态")
    public Result<Map<String, Object>> getConfigStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("customConfigEnabled", configManager.isCustomConfigEnabled());
        status.put("configSummary", configManager.getConfigSummary());
        status.put("endpoint", configManager.getEndpoint());
        status.put("bucketName", configManager.getBucketName());
        status.put("accessPath", configManager.getAccessPath());
        
        return Result.SUCCESS(status);
    }
    
    /**
     * 获取当前配置详情（脱敏）
     */
    @GetMapping("/details")
    @Operation(summary = "获取配置详情")
    public Result<Map<String, Object>> getConfigDetails() {
        Map<String, Object> details = new HashMap<>();
        details.put("enabled", ossProperties.isEnabled());
        details.put("endpoint", configManager.getEndpoint());
        details.put("accessKeyId", maskSensitiveInfo(configManager.getAccessKeyId()));
        details.put("bucketName", configManager.getBucketName());
        details.put("accessPath", configManager.getAccessPath());
        details.put("connectionTimeout", configManager.getConnectionTimeout());
        details.put("socketTimeout", configManager.getSocketTimeout());
        details.put("maxErrorRetry", configManager.getMaxErrorRetry());
        
        return Result.SUCCESS(details);
    }
    
    /**
     * 启用/禁用自定义配置
     */
    @PostMapping("/toggle")
    @Operation(summary = "切换配置模式")
    public Result<String> toggleConfig(@RequestParam boolean enabled) {
        try {
            ossProperties.setEnabled(enabled);
            
            // 重新创建OSS客户端以应用新配置
            clientManager.recreateClient();
            
            String message = enabled ? "已启用自定义OSS配置" : "已切换回原有硬编码配置";
            log.info("配置切换成功: {}", message);
            
            return Result.SUCCESS(message + " - " + configManager.getConfigSummary());
        } catch (Exception e) {
            log.error("配置切换失败", e);
            return Result.ERROR("配置切换失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新自定义配置
     */
    @PostMapping("/update")
    @Operation(summary = "更新自定义配置")
    public Result<String> updateConfig(@RequestBody DigitalOssProperties newConfig) {
        try {
            // 更新配置
            ossProperties.setEndpoint(newConfig.getEndpoint());
            ossProperties.setAccessKeyId(newConfig.getAccessKeyId());
            ossProperties.setSecretAccessKey(newConfig.getSecretAccessKey());
            ossProperties.setBucketName(newConfig.getBucketName());
            ossProperties.setAccessPath(newConfig.getAccessPath());
            ossProperties.setConnectionTimeout(newConfig.getConnectionTimeout());
            ossProperties.setSocketTimeout(newConfig.getSocketTimeout());
            ossProperties.setMaxErrorRetry(newConfig.getMaxErrorRetry());
            
            // 如果当前启用了自定义配置，重新创建客户端
            if (ossProperties.isEnabled()) {
                clientManager.recreateClient();
            }
            
            log.info("OSS配置更新成功: {}", configManager.getConfigSummary());
            return Result.SUCCESS("配置更新成功");
        } catch (Exception e) {
            log.error("配置更新失败", e);
            return Result.ERROR("配置更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试OSS连接
     */
    @PostMapping("/test")
    @Operation(summary = "测试OSS连接")
    public Result<String> testConnection() {
        try {
            clientManager.checkAndReinitialize();
            return Result.SUCCESS("OSS连接测试成功");
        } catch (Exception e) {
            log.error("OSS连接测试失败", e);
            return Result.ERROR("OSS连接测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 脱敏敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 8) {
            return "***";
        }
        return info.substring(0, 4) + "***" + info.substring(info.length() - 4);
    }
}
