package com.business.utils;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * BOssUtil的配置感知增强版
 * 保持所有原有方法，但支持动态配置
 */
@Slf4j
@Component
public class BOssUtilEnhanced {
    
    // 注入配置管理器（如果存在）
    private static Object digitalOssConfigManager;
    
    /**
     * 设置配置管理器（由Spring容器调用）
     */
    @Autowired(required = false)
    public void setDigitalOssConfigManager(Object configManager) {
        digitalOssConfigManager = configManager;
        log.info("BOssUtil增强版已注入配置管理器: {}", configManager != null ? "已启用" : "未启用");
    }
    
    // 增强版的数字人功能专用OSS实例管理
    public static class EnhancedDigitalOssClientHolder {
        private static volatile OSS instance;
        private static final Object lock = new Object();
        private static final ClientBuilderConfiguration config = createClientConfig();
        
        private static ClientBuilderConfiguration createClientConfig() {
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            
            // 如果有配置管理器，使用其配置
            if (digitalOssConfigManager != null) {
                try {
                    // 使用反射调用配置管理器的方法
                    Object connectionTimeout = digitalOssConfigManager.getClass().getMethod("getConnectionTimeout").invoke(digitalOssConfigManager);
                    Object socketTimeout = digitalOssConfigManager.getClass().getMethod("getSocketTimeout").invoke(digitalOssConfigManager);
                    Object maxErrorRetry = digitalOssConfigManager.getClass().getMethod("getMaxErrorRetry").invoke(digitalOssConfigManager);
                    
                    conf.setConnectionTimeout((Integer) connectionTimeout);
                    conf.setSocketTimeout((Integer) socketTimeout);
                    conf.setMaxErrorRetry((Integer) maxErrorRetry);
                    
                    log.debug("使用配置管理器的超时设置");
                } catch (Exception e) {
                    log.warn("获取配置管理器设置失败，使用默认配置", e);
                    setDefaultConfig(conf);
                }
            } else {
                setDefaultConfig(conf);
            }
            
            // 其他固定配置
            conf.setMaxConnections(200);
            conf.setIdleConnectionTime(300);
            conf.setConnectionTTL(600_000);
            conf.setSupportCname(true);
            return conf;
        }
        
        private static void setDefaultConfig(ClientBuilderConfiguration conf) {
            conf.setConnectionTimeout(30_000);
            conf.setSocketTimeout(60_000);
            conf.setMaxErrorRetry(5);
        }
        
        public static OSS getInstance() {
            if (instance == null) {
                synchronized (lock) {
                    if (instance == null) {
                        instance = createOssClient();
                        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                            shutdown();
                        }));
                    }
                }
            }
            return instance;
        }

        private static OSS createOssClient() {
            // 如果有配置管理器且启用了自定义配置，使用新配置
            if (digitalOssConfigManager != null) {
                try {
                    // 检查是否启用了自定义配置
                    Object isCustomEnabled = digitalOssConfigManager.getClass().getMethod("isCustomConfigEnabled").invoke(digitalOssConfigManager);
                    
                    if (Boolean.TRUE.equals(isCustomEnabled)) {
                        // 使用配置管理器的配置
                        Object endpoint = digitalOssConfigManager.getClass().getMethod("getEndpoint").invoke(digitalOssConfigManager);
                        Object accessKeyId = digitalOssConfigManager.getClass().getMethod("getAccessKeyId").invoke(digitalOssConfigManager);
                        Object secretAccessKey = digitalOssConfigManager.getClass().getMethod("getSecretAccessKey").invoke(digitalOssConfigManager);
                        
                        log.info("使用Digital自定义OSS配置创建客户端");
                        return new OSSClientBuilder().build(
                            (String) endpoint,
                            (String) accessKeyId,
                            (String) secretAccessKey,
                            config
                        );
                    }
                } catch (Exception e) {
                    log.warn("获取自定义配置失败，使用原有配置", e);
                }
            }
            
            // 使用原有硬编码配置
            log.debug("使用原有硬编码OSS配置创建客户端");
            return new OSSClientBuilder().build(
                OssClientConfig.ENDPOINT,
                OssClientConfig.ACCESSKEYID,
                OssClientConfig.SECRETACCESSKEY,
                config
            );
        }

        public static void shutdown() {
            synchronized (lock) {
                if (instance != null) {
                    try {
                        instance.shutdown();
                        instance = null;
                        log.info("增强版OSS客户端已正确释放");
                    } catch (Exception e) {
                        log.error("关闭增强版OSS客户端时发生异常", e);
                    }
                }
            }
        }

        public static void checkAndReinitialize() {
            synchronized (lock) {
                try {
                    String bucketName = getBucketName();
                    if (instance != null && !instance.doesBucketExist(bucketName)) {
                        log.warn("OSS连接异常，尝试重建连接...");
                        shutdown();
                        instance = createOssClient();
                        log.info("OSS连接已重建");
                    }
                } catch (Exception e) {
                    log.error("检查OSS连接状态时发生异常", e);
                    shutdown();
                    instance = createOssClient();
                }
            }
        }
        
        /**
         * 强制重新创建客户端（配置变更时使用）
         */
        public static void recreateClient() {
            synchronized (lock) {
                log.info("强制重新创建OSS客户端");
                shutdown();
                instance = createOssClient();
                log.info("OSS客户端重新创建完成");
            }
        }
        
        /**
         * 获取当前使用的bucket名称
         */
        private static String getBucketName() {
            if (digitalOssConfigManager != null) {
                try {
                    Object isCustomEnabled = digitalOssConfigManager.getClass().getMethod("isCustomConfigEnabled").invoke(digitalOssConfigManager);
                    if (Boolean.TRUE.equals(isCustomEnabled)) {
                        Object bucketName = digitalOssConfigManager.getClass().getMethod("getBucketName").invoke(digitalOssConfigManager);
                        return (String) bucketName;
                    }
                } catch (Exception e) {
                    log.warn("获取自定义bucket名称失败", e);
                }
            }
            return OssClientConfig.BUCKET_NAME;
        }
    }
    
    /**
     * 获取增强版数字人功能专用的OSS实例
     */
    public static OSS getDigitalOssInstance() {
        OSS ossClient = EnhancedDigitalOssClientHolder.getInstance();
        EnhancedDigitalOssClientHolder.checkAndReinitialize();
        return ossClient;
    }
    
    /**
     * 获取当前配置状态
     */
    public static String getConfigStatus() {
        if (digitalOssConfigManager != null) {
            try {
                Object summary = digitalOssConfigManager.getClass().getMethod("getConfigSummary").invoke(digitalOssConfigManager);
                return (String) summary;
            } catch (Exception e) {
                return "配置管理器可用，但获取状态失败";
            }
        }
        return "使用原有硬编码配置";
    }
}
